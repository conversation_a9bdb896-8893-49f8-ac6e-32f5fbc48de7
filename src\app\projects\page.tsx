'use client';

import { useState, useEffect } from 'react';
import { AppHeader } from '@/components/app-header';
import { ProjectsTabs } from '@/components/projects-tabs';
// Mock project interface
interface Project {
  id?: string;
  name: string;
  location: string;
  type: string;
  status: string;
  completion?: string;
  description?: string;
  building_consent?: string;
  resource_consent?: string;
  topo_start?: string;
  topo_completed?: string;
  epa?: string;
  works_over?: string;
  works_over_number?: string;
  start_date?: string;
  completion_date?: string;
  estimated_budget?: string;
  actual_cost?: string;
  sale_price?: string;
  lender?: string;
  existing_dwellings?: string;
  new_dwellings?: string;
  client_name?: string;
  project_manager?: string;
  created_at?: string;
  updated_at?: string;
}
import { Skeleton } from '@/components/ui/skeleton';
import { getProjects } from '@/services/project-service';

// Fallback sample project data in case API fails
const sampleProjects = [
  {
    id: 'cm4sample1',
    name: 'Dreadon Road Development',
    location: '33b Dreadon Road, Manurewa',
    type: 'Standalone',
    status: 'In Progress',
    completion: 'December 15, 2025',
    description: 'A modern standalone home development project located in Manurewa. The project includes a 3-bedroom house with modern amenities and sustainable features.',
    sale_price: '$750,000'
  },
  {
    id: 'cm4sample2',
    name: 'Oakridge Apartments',
    location: '45 Oakridge Blvd, Auckland',
    type: 'Apartments',
    status: 'For Sale',
    completion: 'June 30, 2026',
    description: 'A luxury apartment complex with 24 units featuring modern designs and premium amenities in a prime Auckland location.',
    sale_price: '$1,200,000'
  },
  {
    id: 'cm4sample3',
    name: 'Sunset Terraces',
    location: '12 Sunset Drive, Hamilton',
    type: 'Terraced House',
    status: 'For Sale',
    completion: 'March 20, 2025',
    description: 'Beautiful terraced houses with modern finishes and private gardens. Perfect for first-time buyers.',
    sale_price: '$650,000'
  },
  {
    id: 'cm4sample4',
    name: 'Commercial Plaza',
    location: '88 Queen Street, Auckland',
    type: 'Other',
    status: 'Completed',
    completion: 'January 10, 2024',
    description: 'Mixed-use commercial development with retail spaces and office units.',
    sale_price: '$2,500,000'
  },
  {
    id: 'cm4sample5',
    name: 'Riverside Homes',
    location: '25 River Road, Tauranga',
    type: 'Standalone',
    status: 'Planning',
    completion: 'September 15, 2025',
    description: 'Eco-friendly standalone homes with river views and sustainable design features.',
    sale_price: '$850,000'
  }
];

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    type: ''
  });

  // Fetch projects from API
  const fetchProjects = async () => {
    try {
      setLoading(true);
      console.log('Fetching projects from API...');

      const { data, error } = await getProjects();

      if (error) {
        console.error('API error:', error);
        setError(`Failed to load projects: ${error}`);
        // Only use sample data if API completely fails
        setProjects([]);
      } else if (data && Array.isArray(data)) {
        console.log('✅ Successfully loaded', data.length, 'projects from database');
        setProjects(data);
        setError(null);
      } else {
        console.log('No data returned from API');
        setProjects([]);
        setError('No projects found');
      }
    } catch (err) {
      console.error('Exception fetching projects:', err);
      setError('An unexpected error occurred. Please try again later.');
      setProjects([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch projects when the component mounts
  useEffect(() => {
    fetchProjects();
  }, []);

  return (
    <div className="flex flex-col h-screen">
      <AppHeader hideSearch={true} />
      <div className="flex-1 mt-[72px] p-6 pb-24 h-screen overflow-y-auto bg-[#f9fdff]">
        <div className="max-w-7xl mx-auto">
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          ) : error ? (
            <div className="p-4 bg-red-50 text-red-700 rounded-md space-y-4">
              <p>{error}</p>
              <p className="text-sm">
                Having trouble? Try refreshing the page or check the{' '}
                <a href="/projects" className="text-blue-600 underline hover:text-blue-800">
                  projects page
                </a>{' '}
                again.
              </p>
            </div>
          ) : (
            <ProjectsTabs
              projects={projects}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              showFilters={showFilters}
              filters={filters}
              onFiltersChange={setFilters}
              onToggleFilters={() => setShowFilters(!showFilters)}
            />
          )}
        </div>
      </div>
    </div>
  );
}






