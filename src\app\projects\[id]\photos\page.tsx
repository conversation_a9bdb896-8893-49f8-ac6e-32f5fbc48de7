'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { NavSidebar } from '@/components/nav-sidebar';
import { AppHeader } from '@/components/app-header';
import { NewProjectButton } from '@/components/new-project-button';
import { PhotoPreview } from '@/components/photo-preview';
import { FolderTree, FolderItem } from '@/components/folder-tree';
import { FileUpload } from '@/components/file-upload';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, Upload, Download, Trash2, Share2, Filter, FolderPlus } from 'lucide-react';
import { toast } from 'sonner';
import { LoadingAnimation, InlineLoading } from '@/components/loading-animation';

interface Photo {
  id: string;
  title: string;
  date: string;
  thumbnail: string;
  file_path: string;
  project_id: string;
  folder: string | null;
}

export default function ProjectPhotosPage() {
  const params = useParams();
  const projectId = params.id as string;

  // Photo state
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for photo management
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [selectedPhotoId, setSelectedPhotoId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);

  // Mock folder data
  const [folders] = useState<FolderItem[]>([
    {
      id: 'general',
      name: 'General',
      type: 'folder',
      children: []
    },
    {
      id: 'progress',
      name: 'Progress Photos',
      type: 'folder',
      children: []
    },
    {
      id: 'before',
      name: 'Before Photos',
      type: 'folder',
      children: []
    },
    {
      id: 'after',
      name: 'After Photos',
      type: 'folder',
      children: []
    }
  ]);

  // Fetch photos for this project
  const fetchPhotos = useCallback(async () => {
    if (!projectId) return;
    
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/photos?projectId=${projectId}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch photos');
      }

      setPhotos(result.data || []);
    } catch (err: any) {
      console.error('Error fetching photos:', err);
      setError(err.message || 'Failed to fetch photos');
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchPhotos();
  }, [fetchPhotos]);

  // Handle photo upload
  const handlePhotoUpload = async (files: File[]) => {
    if (!projectId) {
      toast.error('No project selected');
      return;
    }

    try {
      setLoading(true);

      for (const file of files) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('projectId', projectId);
        formData.append('folder', currentFolderId || 'General');

        const response = await fetch('/api/photos', {
          method: 'POST',
          body: formData,
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || `Failed to upload ${file.name}`);
        }
      }

      toast.success(`Successfully uploaded ${files.length} photo(s)`);
      await fetchPhotos(); // Refresh the list
    } catch (err: any) {
      console.error('Error uploading photos:', err);
      toast.error(err.message || 'Failed to upload photos');
    } finally {
      setLoading(false);
    }
  };

  // Filter photos based on search term and current folder
  const filteredPhotos = photos.filter(photo => {
    const matchesSearch = photo.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFolder = !currentFolderId || photo.folder === currentFolderId;
    return matchesSearch && matchesFolder;
  });

  // Get selected photo for preview
  const selectedPhoto = selectedPhotoId ? photos.find(p => p.id === selectedPhotoId) : null;

  // Handle folder operations
  const handleFolderClick = (item: FolderItem) => {
    setCurrentFolderId(item.id === currentFolderId ? null : item.id);
  };

  const createFolder = (name: string, parentId?: string) => {
    toast.success(`Folder "${name}" created`);
  };

  const renameItem = (id: string, newName: string) => {
    toast.success(`Renamed to "${newName}"`);
  };

  const deleteItem = (id: string) => {
    toast.success('Item deleted');
  };

  // Handle photo selection
  const handlePhotoSelect = (photoId: string) => {
    setSelectedPhotos(prev => 
      prev.includes(photoId)
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    );
  };

  const handlePhotoClick = (photoId: string) => {
    setSelectedPhotoId(photoId);
  };

  const handleSelectAll = () => {
    if (selectedPhotos.length === filteredPhotos.length) {
      setSelectedPhotos([]);
    } else {
      setSelectedPhotos(filteredPhotos.map(photo => photo.id));
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <AppHeader />
      <div className="flex">
        <NavSidebar />
        <div className="ml-60 mt-[72px] w-full h-screen flex overflow-hidden">
          {/* Folder Tree Sidebar */}
          <div className="w-64 p-4 border-r bg-gray-50 overflow-auto">
            <FolderTree
              items={folders}
              onItemClick={handleFolderClick}
              onCreateFolder={createFolder}
              onRenameItem={renameItem}
              onDeleteItem={deleteItem}
              selectedItemId={currentFolderId || undefined}
            />
          </div>

          {/* Photo List Section */}
          <div className="w-80 p-4 border-r bg-white overflow-auto">
            <div className="mb-4 flex justify-between items-center">
              <div>
                <h1 className="text-lg font-bold text-blue-700">Project Photos</h1>
                <p className="text-gray-600 mt-0.5 text-xs">JPEG/PNG photos for this project</p>
              </div>
            </div>

            {/* Search and Upload Section */}
            <div className="mb-4 space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search photos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white text-sm"
                />
              </div>
              <FileUpload
                onUpload={handlePhotoUpload}
                acceptedFileTypes="image/*"
                multiple={true}
                maxFiles={10}
                maxSize={5}
                buttonText="Upload Photos"
                buttonIcon={<Upload className="h-4 w-4 mr-1" />}
                buttonClassName="bg-[#0271c3] hover:bg-[#0271c3]/90 text-white w-full"
                buttonSize="sm"
              />
            </div>

            {/* Photo List */}
            {loading ? (
              <InlineLoading text="Loading photos..." className="py-8" />
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-500 text-sm">{error}</p>
                <Button onClick={fetchPhotos} className="mt-2" size="sm">
                  Retry
                </Button>
              </div>
            ) : filteredPhotos.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 text-sm">
                  {searchTerm ? 'No photos match your search.' : 'No photos uploaded yet.'}
                </p>
                {!searchTerm && (
                  <p className="text-xs text-gray-400 mt-2">
                    Upload JPEG/PNG photos to get started.
                  </p>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {/* Select All Checkbox */}
                <div className="flex items-center">
                  <Checkbox
                    id="select-all-photos"
                    checked={selectedPhotos.length === filteredPhotos.length}
                    onCheckedChange={handleSelectAll}
                    className="mr-2"
                  />
                  <label htmlFor="select-all-photos" className="text-sm font-medium text-gray-700">
                    Select All ({filteredPhotos.length} photos)
                  </label>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {filteredPhotos.map((photo) => (
                    <div
                      key={photo.id}
                      className={`border border-slate-400 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer ${
                        selectedPhotoId === photo.id ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => handlePhotoClick(photo.id)}
                    >
                      <div className="relative">
                        <div className="absolute top-2 left-2 z-10">
                          <Checkbox
                            checked={selectedPhotos.includes(photo.id)}
                            onCheckedChange={() => handlePhotoSelect(photo.id)}
                            onClick={(e) => e.stopPropagation()}
                            className="bg-white/80 border-white"
                          />
                        </div>
                        <div className="aspect-video bg-gray-100 flex items-center justify-center">
                          {photo.file_path ? (
                            <img
                              src={`/uploads/photos/${photo.file_path}`}
                              alt={photo.title}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling?.classList.remove('hidden');
                              }}
                            />
                          ) : null}
                          <div className="text-gray-400 text-xs hidden">
                            No preview
                          </div>
                        </div>
                      </div>
                      <div className="p-3">
                        <h3 className="font-medium text-sm">{photo.title}</h3>
                        <p className="text-xs text-gray-500 mt-0.5">{photo.date}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Photo Preview Section */}
          <div className="flex-1 p-4 pb-20 overflow-auto bg-[#f9fdff]">
            <div className="mb-4 flex justify-between items-center">
              <div>
                <h2 className="text-lg font-semibold text-blue-700">
                  {selectedPhoto ? selectedPhoto.title : 'Photo Preview'}
                </h2>
                {selectedPhoto && (
                  <p className="text-sm text-gray-600 mt-1">
                    {selectedPhoto.date} • {selectedPhoto.folder || 'General'}
                  </p>
                )}
              </div>
              <div>
                <NewProjectButton onProjectCreated={fetchPhotos} />
              </div>
            </div>

            <PhotoPreview
              photo={selectedPhoto}
              onShare={(photo) => {
                setSelectedPhotos([photo.id]);
                // Add share functionality here if needed
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
