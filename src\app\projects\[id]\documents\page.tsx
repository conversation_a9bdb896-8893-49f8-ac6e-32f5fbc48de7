'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { NavSidebar } from '@/components/nav-sidebar';
import { AppHeader } from '@/components/app-header';
import { NewProjectButton } from '@/components/new-project-button';
import { ShareDialog } from '@/components/share-dialog';
import { EnhancedShareDialog } from '@/components/enhanced-share-dialog';
import { FolderTree, FolderItem } from '@/components/folder-tree';
import { FileUpload } from '@/components/file-upload';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, Download, Trash2, Share2, Filter, Upload, FolderPlus } from 'lucide-react';
import { toast } from 'sonner';

interface Document {
  id: string;
  name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  folder: string | null;
  project_id: string;
  created_at: string;
  updated_at: string;
}

export default function ProjectDocumentsPage() {
  const params = useParams();
  const projectId = params.id as string;

  // Document state
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for document management
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [isEnhancedShareDialogOpen, setIsEnhancedShareDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);

  // Mock folder data
  const [folders] = useState<FolderItem[]>([
    {
      id: 'general',
      name: 'General',
      type: 'folder',
      children: []
    },
    {
      id: 'permits',
      name: 'Permits & Approvals',
      type: 'folder',
      children: []
    },
    {
      id: 'contracts',
      name: 'Contracts',
      type: 'folder',
      children: []
    },
    {
      id: 'plans',
      name: 'Plans & Drawings',
      type: 'folder',
      children: []
    }
  ]);

  // Fetch documents for this project
  const fetchDocuments = useCallback(async () => {
    if (!projectId) return;
    
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/documents?projectId=${projectId}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch documents');
      }

      setDocuments(result.data || []);
    } catch (err: any) {
      console.error('Error fetching documents:', err);
      setError(err.message || 'Failed to fetch documents');
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  // Handle document upload
  const handleDocumentUpload = async (files: File[]) => {
    if (!projectId) {
      toast.error('No project selected');
      return;
    }

    try {
      setLoading(true);

      for (const file of files) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('projectId', projectId);
        formData.append('folder', currentFolderId || 'General');

        const response = await fetch('/api/documents', {
          method: 'POST',
          body: formData,
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || `Failed to upload ${file.name}`);
        }
      }

      toast.success(`Successfully uploaded ${files.length} document(s)`);
      await fetchDocuments(); // Refresh the list
    } catch (err: any) {
      console.error('Error uploading documents:', err);
      toast.error(err.message || 'Failed to upload documents');
    } finally {
      setLoading(false);
    }
  };

  // Filter documents based on search term and current folder
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFolder = !currentFolderId || doc.folder === currentFolderId;
    return matchesSearch && matchesFolder;
  });

  // Handle folder operations
  const handleFolderClick = (folderId: string) => {
    setCurrentFolderId(folderId === currentFolderId ? null : folderId);
  };

  const createFolder = (name: string, parentId?: string) => {
    toast.success(`Folder "${name}" created`);
  };

  const renameItem = (id: string, newName: string) => {
    toast.success(`Renamed to "${newName}"`);
  };

  const deleteItem = (id: string) => {
    toast.success('Item deleted');
  };

  // Handle document selection
  const handleDocumentSelect = (documentId: string) => {
    setSelectedDocuments(prev => 
      prev.includes(documentId)
        ? prev.filter(id => id !== documentId)
        : [...prev, documentId]
    );
  };

  const handleSelectAll = () => {
    if (selectedDocuments.length === filteredDocuments.length) {
      setSelectedDocuments([]);
    } else {
      setSelectedDocuments(filteredDocuments.map(doc => doc.id));
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="flex flex-col h-screen">
      <AppHeader />
      <div className="flex">
        <NavSidebar />
        <div className="ml-60 mt-[72px] w-full h-screen flex overflow-hidden">
          {/* Folder Tree Sidebar */}
          <div className="w-64 p-4 border-r bg-gray-50 overflow-auto">
            <FolderTree
              items={folders}
              onItemClick={handleFolderClick}
              onCreateFolder={createFolder}
              onRenameItem={renameItem}
              onDeleteItem={deleteItem}
              selectedItemId={currentFolderId}
            />
          </div>

          {/* Document List Section */}
          <div className="flex-1 p-4 pb-20 overflow-auto">
            <div className="mb-4 flex justify-between items-center">
              <div>
                <h1 className="text-xl font-bold text-blue-700">Project Documents</h1>
                <p className="text-gray-600 mt-0.5 text-sm">Manage documents for this project (PDF only)</p>
              </div>
              <div>
                <NewProjectButton />
              </div>
            </div>

            {/* Search and Upload Section */}
            <div className="mb-4 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white"
                />
              </div>
              <div className="flex gap-2">
                <FileUpload
                  onUpload={handleDocumentUpload}
                  acceptedFileTypes="application/pdf"
                  multiple={true}
                  maxFiles={10}
                  maxSize={10}
                  buttonText="Upload PDF"
                  buttonIcon={<Upload className="h-4 w-4 mr-1" />}
                  buttonClassName="bg-[#0271c3] hover:bg-[#0271c3]/90 text-white"
                />
              </div>
            </div>

            {/* Document List */}
            {loading ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Loading documents...</p>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-500">{error}</p>
                <Button onClick={fetchDocuments} className="mt-2">
                  Retry
                </Button>
              </div>
            ) : filteredDocuments.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">
                  {searchTerm ? 'No documents match your search.' : 'No documents uploaded yet.'}
                </p>
                {!searchTerm && (
                  <p className="text-sm text-gray-400 mt-2">
                    Upload PDF documents to get started.
                  </p>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {/* Select All Checkbox */}
                <div className="flex items-center">
                  <Checkbox
                    id="select-all-documents"
                    checked={selectedDocuments.length === filteredDocuments.length}
                    onCheckedChange={handleSelectAll}
                    className="mr-2"
                  />
                  <label htmlFor="select-all-documents" className="text-sm font-medium text-gray-700">
                    Select All ({filteredDocuments.length} documents)
                  </label>
                </div>

                {/* Document Grid */}
                <div className="grid grid-cols-1 gap-3">
                  {filteredDocuments.map((document) => (
                    <div
                      key={document.id}
                      className="flex items-center p-4 border border-slate-400 rounded-lg hover:shadow-md transition-shadow bg-white"
                    >
                      <Checkbox
                        checked={selectedDocuments.includes(document.id)}
                        onCheckedChange={() => handleDocumentSelect(document.id)}
                        className="mr-3"
                      />
                      <div className="flex-1">
                        <h3 className="font-medium text-sm">{document.name}</h3>
                        <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                          <span>{formatFileSize(document.file_size)}</span>
                          <span>{formatDate(document.created_at)}</span>
                          {document.folder && <span>Folder: {document.folder}</span>}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Share2 className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Share Dialogs */}
      <ShareDialog
        isOpen={isShareDialogOpen}
        onClose={() => setIsShareDialogOpen(false)}
        selectedItems={selectedDocuments}
        itemType="document"
      />

      <EnhancedShareDialog
        isOpen={isEnhancedShareDialogOpen}
        onClose={() => setIsEnhancedShareDialogOpen(false)}
        selectedItems={selectedDocuments}
        itemType="document"
      />
    </div>
  );
}
