// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Project {
  id                  String   @id @default(cuid())
  name                String
  location            String?
  type                String?
  status              String   @default("Planning")
  completion          String?
  description         String?
  building_consent    String?
  resource_consent    String?
  topo_start          String?
  topo_completed      String?
  epa                 String?
  works_over          String?
  works_over_number   String?
  start_date          String?
  completion_date     String?
  estimated_budget    String?
  actual_cost         String?
  sale_price          String?
  lender              String?
  existing_dwellings  String?
  new_dwellings       String?
  client_name         String?
  project_manager     String?
  created_at          DateTime @default(now())
  updated_at          DateTime @updatedAt

  // Relations
  documents Document[]
  photos    Photo[]

  @@map("projects")
}

model Document {
  id          String   @id @default(cuid())
  project_id  String
  name        String
  file_path   String
  file_size   Int?
  mime_type   String?
  folder      String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@map("documents")
}

model Photo {
  id          String   @id @default(cuid())
  project_id  String
  title       String
  file_path   String
  file_size   Int?
  mime_type   String?
  folder      String?
  thumbnail   String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@map("photos")
}
