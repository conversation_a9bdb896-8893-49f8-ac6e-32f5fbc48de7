// Mock project service - no database connections

export interface Project {
  id?: string;
  name: string;
  location: string;
  type: string;
  status: string;
  completion?: string;
  description?: string;
  building_consent?: string;
  resource_consent?: string;
  topo_start?: string;
  topo_completed?: string;
  epa?: string;
  works_over?: string;
  works_over_number?: string;
  start_date?: string;
  completion_date?: string;
  estimated_budget?: string;
  actual_cost?: string;
  sale_price?: string;
  lender?: string;
  existing_dwellings?: string;
  new_dwellings?: string;
  client_name?: string;
  project_manager?: string;
  created_at?: string;
  updated_at?: string;
}

export async function createProject(project: Project): Promise<{ data: Project | null; error: any }> {
  try {
    console.log('Mock create - project would be created:', project);
    
    const projectWithTimestamps = {
      ...project,
      id: Math.random().toString(36).substring(2, 11),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    await new Promise(resolve => setTimeout(resolve, 500));
    return { data: projectWithTimestamps, error: null };
  } catch (error) {
    console.error('Error in createProject:', error);
    return { data: null, error };
  }
}

export async function getProjects(): Promise<{ data: Project[] | null; error: any }> {
  try {
    console.log('Returning mock projects data');
    await new Promise(resolve => setTimeout(resolve, 300));
    
    return {
      data: [
        {
          id: '1',
          name: 'Dreadon Road Development',
          location: '33b Dreadon Road, Manurewa',
          type: 'Standalone',
          status: 'In Progress',
          description: 'A modern standalone home development project located in Manurewa.',
          completion: 'December 15, 2025',
          sale_price: '$750,000'
        },
        {
          id: '2',
          name: 'Oakridge Apartments',
          location: '45 Oakridge Blvd, Auckland',
          type: 'Apartments',
          status: 'For Sale',
          description: 'A luxury apartment complex with 24 units featuring modern designs and premium amenities.',
          completion: 'June 30, 2026',
          sale_price: '$1,200,000'
        },
        {
          id: '3',
          name: 'Sunset Terraces',
          location: '12 Sunset Drive, Hamilton',
          type: 'Terraced House',
          status: 'For Sale',
          description: 'Beautiful terraced houses with modern finishes and private gardens.',
          completion: 'March 20, 2025',
          sale_price: '$650,000'
        },
        {
          id: '4',
          name: 'Commercial Plaza',
          location: '88 Queen Street, Auckland',
          type: 'Other',
          status: 'Completed',
          description: 'Mixed-use commercial development with retail spaces and office units.',
          completion: 'January 10, 2024',
          sale_price: '$2,500,000'
        },
        {
          id: '5',
          name: 'Riverside Homes',
          location: '25 River Road, Tauranga',
          type: 'Standalone',
          status: 'Planning',
          description: 'Eco-friendly standalone homes with river views and sustainable design features.',
          completion: 'September 15, 2025',
          sale_price: '$850,000'
        }
      ],
      error: null
    };
  } catch (error) {
    console.error('Error in getProjects:', error);
    return { data: null, error };
  }
}

export async function getProjectById(id: string): Promise<{ data: Project | null; error: any }> {
  try {
    console.log(`Returning mock data for project ${id}`);
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const mockProjects = {
      '1': {
        id: '1',
        name: 'Dreadon Road Development',
        location: '33b Dreadon Road, Manurewa',
        type: 'Standalone',
        status: 'In Progress',
        description: 'A modern standalone home development project located in Manurewa.',
        completion: 'December 15, 2025',
        sale_price: '$750,000',
        client_name: 'John Smith',
        project_manager: 'Jane Doe',
        start_date: '2023-01-15',
        completion_date: '2024-12-31',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      '2': {
        id: '2',
        name: 'Oakridge Apartments',
        location: '45 Oakridge Blvd, Auckland',
        type: 'Apartments',
        status: 'For Sale',
        description: 'A luxury apartment complex with 24 units featuring modern designs and premium amenities.',
        completion: 'June 30, 2026',
        sale_price: '$1,200,000',
        client_name: 'Sarah Johnson',
        project_manager: 'Mike Wilson',
        start_date: '2023-03-01',
        completion_date: '2026-06-30',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      '3': {
        id: '3',
        name: 'Sunset Terraces',
        location: '12 Sunset Drive, Hamilton',
        type: 'Terraced House',
        status: 'For Sale',
        description: 'Beautiful terraced houses with modern finishes and private gardens.',
        completion: 'March 20, 2025',
        sale_price: '$650,000',
        client_name: 'David Brown',
        project_manager: 'Lisa Chen',
        start_date: '2023-06-01',
        completion_date: '2025-03-20',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    };

    return {
      data: mockProjects[id as keyof typeof mockProjects] || mockProjects['1'],
      error: null
    };
  } catch (error) {
    console.error(`Error in getProjectById for ${id}:`, error);
    return {
      data: {
        id: id,
        name: 'Mock Project',
        location: 'Auckland, New Zealand',
        type: 'Standalone',
        status: 'In Progress',
        description: 'This is a mock project created when the database connection failed.',
        completion: 'December 31, 2025',
        sale_price: '$500,000',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      error: null
    };
  }
}

export async function updateProject(id: string, project: Partial<Project>): Promise<{ data: Project | null; error: any }> {
  try {
    console.log('Mock update - project data would be saved:', { id, project });
    
    const projectWithTimestamp = {
      ...project,
      id: id,
      updated_at: new Date().toISOString(),
    };

    await new Promise(resolve => setTimeout(resolve, 500));
    return { 
      data: projectWithTimestamp as Project, 
      error: null 
    };
  } catch (error) {
    console.error('Error in updateProject:', error);
    return { data: null, error };
  }
}

export async function deleteProject(id: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Mock delete - project would be deleted:', id);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, error: null };
  } catch (error) {
    console.error('Error in deleteProject:', error);
    return { success: false, error };
  }
}
