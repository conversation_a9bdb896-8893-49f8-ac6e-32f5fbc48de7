import { prisma } from '@/lib/prisma';
import type { Project as PrismaProject } from '@prisma/client';

export interface Project {
  id?: string;
  name: string;
  location: string;
  type: string;
  status: string;
  completion?: string;
  description?: string;
  building_consent?: string;
  resource_consent?: string;
  topo_start?: string;
  topo_completed?: string;
  epa?: string;
  works_over?: string;
  works_over_number?: string;
  start_date?: string;
  completion_date?: string;
  estimated_budget?: string;
  actual_cost?: string;
  sale_price?: string;
  lender?: string;
  existing_dwellings?: string;
  new_dwellings?: string;
  client_name?: string;
  project_manager?: string;
  created_at?: string;
  updated_at?: string;
}

export async function createProject(project: Project): Promise<{ data: Project | null; error: any }> {
  try {
    console.log('Creating project:', project);

    const createdProject = await prisma.project.create({
      data: {
        name: project.name,
        location: project.location || null,
        type: project.type || null,
        status: project.status || 'Planning',
        completion: project.completion || null,
        description: project.description || null,
        building_consent: project.building_consent || null,
        resource_consent: project.resource_consent || null,
        topo_start: project.topo_start || null,
        topo_completed: project.topo_completed || null,
        epa: project.epa || null,
        works_over: project.works_over || null,
        works_over_number: project.works_over_number || null,
        start_date: project.start_date || null,
        completion_date: project.completion_date || null,
        estimated_budget: project.estimated_budget || null,
        actual_cost: project.actual_cost || null,
        sale_price: project.sale_price || null,
        lender: project.lender || null,
        existing_dwellings: project.existing_dwellings || null,
        new_dwellings: project.new_dwellings || null,
        client_name: project.client_name || null,
        project_manager: project.project_manager || null,
      },
    });

    const projectData: Project = {
      ...createdProject,
      created_at: createdProject.created_at.toISOString(),
      updated_at: createdProject.updated_at.toISOString(),
    };

    return { data: projectData, error: null };
  } catch (error) {
    console.error('Error in createProject:', error);
    return { data: null, error };
  }
}

export async function getProjects(): Promise<{ data: Project[] | null; error: any }> {
  try {
    console.log('Fetching projects from database');

    const projects = await prisma.project.findMany({
      orderBy: {
        created_at: 'desc',
      },
    });

    const projectsData: Project[] = projects.map((project: any) => ({
      ...project,
      created_at: project.created_at.toISOString(),
      updated_at: project.updated_at.toISOString(),
    }));

    return { data: projectsData, error: null };
  } catch (error) {
    console.error('Error in getProjects:', error);
    return { data: null, error };
  }
}

export async function getProjectById(id: string): Promise<{ data: Project | null; error: any }> {
  try {
    console.log(`Fetching project with id: ${id}`);

    const project = await prisma.project.findUnique({
      where: {
        id: id,
      },
    });

    if (!project) {
      return { data: null, error: new Error('Project not found') };
    }

    const projectData: Project = {
      ...project,
      created_at: project.created_at.toISOString(),
      updated_at: project.updated_at.toISOString(),
    };

    return { data: projectData, error: null };
  } catch (error) {
    console.error(`Error in getProjectById for ${id}:`, error);
    return { data: null, error };
  }
}

export async function updateProject(id: string, project: Partial<Project>): Promise<{ data: Project | null; error: any }> {
  try {
    console.log('Updating project:', { id, project });

    const updatedProject = await prisma.project.update({
      where: {
        id: id,
      },
      data: {
        ...(project.name && { name: project.name }),
        ...(project.location !== undefined && { location: project.location }),
        ...(project.type !== undefined && { type: project.type }),
        ...(project.status !== undefined && { status: project.status }),
        ...(project.completion !== undefined && { completion: project.completion }),
        ...(project.description !== undefined && { description: project.description }),
        ...(project.building_consent !== undefined && { building_consent: project.building_consent }),
        ...(project.resource_consent !== undefined && { resource_consent: project.resource_consent }),
        ...(project.topo_start !== undefined && { topo_start: project.topo_start }),
        ...(project.topo_completed !== undefined && { topo_completed: project.topo_completed }),
        ...(project.epa !== undefined && { epa: project.epa }),
        ...(project.works_over !== undefined && { works_over: project.works_over }),
        ...(project.works_over_number !== undefined && { works_over_number: project.works_over_number }),
        ...(project.start_date !== undefined && { start_date: project.start_date }),
        ...(project.completion_date !== undefined && { completion_date: project.completion_date }),
        ...(project.estimated_budget !== undefined && { estimated_budget: project.estimated_budget }),
        ...(project.actual_cost !== undefined && { actual_cost: project.actual_cost }),
        ...(project.sale_price !== undefined && { sale_price: project.sale_price }),
        ...(project.lender !== undefined && { lender: project.lender }),
        ...(project.existing_dwellings !== undefined && { existing_dwellings: project.existing_dwellings }),
        ...(project.new_dwellings !== undefined && { new_dwellings: project.new_dwellings }),
        ...(project.client_name !== undefined && { client_name: project.client_name }),
        ...(project.project_manager !== undefined && { project_manager: project.project_manager }),
      },
    });

    const projectData: Project = {
      ...updatedProject,
      created_at: updatedProject.created_at.toISOString(),
      updated_at: updatedProject.updated_at.toISOString(),
    };

    return { data: projectData, error: null };
  } catch (error) {
    console.error('Error in updateProject:', error);
    return { data: null, error };
  }
}

export async function deleteProject(id: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Deleting project:', id);

    await prisma.project.delete({
      where: {
        id: id,
      },
    });

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in deleteProject:', error);
    return { success: false, error };
  }
}
