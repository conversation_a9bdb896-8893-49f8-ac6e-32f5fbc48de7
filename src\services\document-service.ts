import { prisma } from '@/lib/prisma';
import { v4 as uuidv4 } from 'uuid';

export interface Document {
  id: string;
  name: string;
  file_path: string;
  file_size?: number;
  mime_type?: string;
  folder?: string;
  project_id: string;
  created_at: string;
  updated_at: string;
}

/**
 * Get documents for a specific project
 */
export async function getDocuments(projectId: string): Promise<{ data: Document[] | null; error: any }> {
  try {
    console.log('Fetching documents for project:', projectId);

    const documents = await prisma.document.findMany({
      where: {
        project_id: projectId,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    const documentsData: Document[] = documents.map((doc: any) => ({
      id: doc.id,
      name: doc.name,
      file_path: doc.file_path,
      file_size: doc.file_size,
      mime_type: doc.mime_type,
      folder: doc.folder,
      project_id: doc.project_id,
      created_at: doc.created_at.toISOString(),
      updated_at: doc.updated_at.toISOString(),
    }));

    return { data: documentsData, error: null };
  } catch (error) {
    console.error('Error in getDocuments:', error);
    return { data: null, error };
  }
}

/**
 * Upload a document (for now, just create a database record)
 * In a real implementation, you would save the file to a local directory
 */
export async function uploadDocument(
  file: File,
  projectId: string,
  folder?: string
): Promise<{ data: Document | null; error: any }> {
  try {
    console.log('Creating document record:', file.name);

    // Generate a unique file path
    const fileId = uuidv4();
    const fileExt = file.name.split('.').pop();
    const filePath = `${fileId}.${fileExt}`;

    // Create document record in database
    const document = await prisma.document.create({
      data: {
        name: file.name,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.type,
        project_id: projectId,
        folder: folder || null,
      },
    });

    const documentData: Document = {
      id: document.id,
      name: document.name,
      file_path: document.file_path,
      file_size: document.file_size,
      mime_type: document.mime_type,
      folder: document.folder,
      project_id: document.project_id,
      created_at: document.created_at.toISOString(),
      updated_at: document.updated_at.toISOString(),
    };

    return { data: documentData, error: null };
  } catch (error) {
    console.error('Error in uploadDocument:', error);
    return { data: null, error };
  }
}

/**
 * Delete a document
 */
export async function deleteDocument(id: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Deleting document:', id);

    await prisma.document.delete({
      where: {
        id: id,
      },
    });

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in deleteDocument:', error);
    return { success: false, error };
  }
}

/**
 * Create a folder for organizing documents
 */
export async function createFolder(name: string, projectId: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Creating folder:', name);

    // For now, folders are just a string field in the document records
    // In a more complex implementation, you might have a separate folders table

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in createFolder:', error);
    return { success: false, error };
  }
}

/**
 * Get documents by folder
 */
export async function getDocumentsByFolder(
  folder: string,
  projectId: string
): Promise<{ data: Document[] | null; error: any }> {
  try {
    console.log('Fetching documents by folder:', folder);

    const documents = await prisma.document.findMany({
      where: {
        folder: folder,
        project_id: projectId,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    const documentsData: Document[] = documents.map((doc: any) => ({
      id: doc.id,
      name: doc.name,
      file_path: doc.file_path,
      file_size: doc.file_size,
      mime_type: doc.mime_type,
      folder: doc.folder,
      project_id: doc.project_id,
      created_at: doc.created_at.toISOString(),
      updated_at: doc.updated_at.toISOString(),
    }));

    return { data: documentsData, error: null };
  } catch (error) {
    console.error('Error in getDocumentsByFolder:', error);
    return { data: null, error };
  }
}
