"use client";

import { useEffect, useState } from 'react';
import <PERSON><PERSON> from 'lottie-react';

// Simple loading animation data (you can replace this with a more complex animation)
const loadingAnimationData = {
  "v": "5.7.4",
  "fr": 30,
  "ip": 0,
  "op": 60,
  "w": 200,
  "h": 200,
  "nm": "Loading",
  "ddd": 0,
  "assets": [],
  "layers": [
    {
      "ddd": 0,
      "ind": 1,
      "ty": 4,
      "nm": "Circle",
      "sr": 1,
      "ks": {
        "o": {"a": 0, "k": 100},
        "r": {
          "a": 1,
          "k": [
            {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]},
            {"t": 60, "s": [360]}
          ]
        },
        "p": {"a": 0, "k": [100, 100, 0]},
        "a": {"a": 0, "k": [0, 0, 0]},
        "s": {"a": 0, "k": [100, 100, 100]}
      },
      "ao": 0,
      "shapes": [
        {
          "ty": "gr",
          "it": [
            {
              "d": 1,
              "ty": "el",
              "s": {"a": 0, "k": [80, 80]},
              "p": {"a": 0, "k": [0, 0]}
            },
            {
              "ty": "st",
              "c": {"a": 0, "k": [0.027, 0.443, 0.765, 1]},
              "o": {"a": 0, "k": 100},
              "w": {"a": 0, "k": 8},
              "lc": 2,
              "lj": 2
            },
            {
              "ty": "tr",
              "p": {"a": 0, "k": [0, 0]},
              "a": {"a": 0, "k": [0, 0]},
              "s": {"a": 0, "k": [100, 100]},
              "r": {"a": 0, "k": 0},
              "o": {"a": 0, "k": 100}
            }
          ]
        }
      ],
      "ip": 0,
      "op": 60,
      "st": 0
    }
  ]
};

interface LoadingAnimationProps {
  size?: number;
  className?: string;
  text?: string;
}

export function LoadingAnimation({ 
  size = 120, 
  className = "", 
  text = "Loading..." 
}: LoadingAnimationProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Small delay to prevent flash for very quick loads
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  if (!isVisible) return null;

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div style={{ width: size, height: size }}>
        <Lottie
          animationData={loadingAnimationData}
          loop={true}
          autoplay={true}
          style={{ width: '100%', height: '100%' }}
        />
      </div>
      {text && (
        <p className="mt-4 text-sm text-gray-600 font-medium animate-pulse">
          {text}
        </p>
      )}
    </div>
  );
}

// Full screen loading overlay
export function LoadingOverlay({ 
  isVisible = true, 
  text = "Loading..." 
}: { 
  isVisible?: boolean; 
  text?: string; 
}) {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-white bg-opacity-90 z-50 flex items-center justify-center">
      <LoadingAnimation size={150} text={text} />
    </div>
  );
}

// Inline loading for content areas
export function InlineLoading({ 
  text = "Loading...", 
  className = "py-12" 
}: { 
  text?: string; 
  className?: string; 
}) {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <LoadingAnimation size={80} text={text} />
    </div>
  );
}
