{"name": "project-management-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^5.22.0", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.9", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.9.7", "lucide-react": "^0.503.0", "next": "15.3.1", "next-themes": "^0.4.6", "pdfjs-dist": "^5.2.133", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-pdf": "^9.2.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "prisma": "^5.22.0", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.2.8", "typescript": "^5"}}