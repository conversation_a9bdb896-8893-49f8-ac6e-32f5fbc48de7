'use client';

import { useEffect, useState } from 'react';
import { getProjects } from '@/services/project-service';
import Link from 'next/link';

interface Project {
  id: string;
  name: string;
  location?: string;
  status?: string;
}

export default function DebugPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchProjects() {
      try {
        const { data, error } = await getProjects();
        
        if (error) {
          setError(error);
        } else if (data) {
          setProjects(data);
        }
      } catch (err) {
        setError('Failed to fetch projects');
      } finally {
        setLoading(false);
      }
    }

    fetchProjects();
  }, []);

  if (loading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Loading projects...</h1>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4 text-red-600">Error</h1>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Debug: Available Projects</h1>
      
      <div className="mb-6">
        <p className="text-gray-600 mb-4">
          Here are all the available projects with their correct URLs:
        </p>
      </div>

      <div className="space-y-4">
        {projects.map((project, index) => (
          <div key={project.id} className="border rounded-lg p-4 bg-gray-50">
            <h3 className="font-semibold text-lg">{index + 1}. {project.name}</h3>
            <p className="text-sm text-gray-600 mb-2">
              Location: {project.location || 'Not specified'}
            </p>
            <p className="text-sm text-gray-600 mb-3">
              Status: {project.status || 'Not specified'}
            </p>
            
            <div className="space-y-2">
              <div>
                <span className="font-medium">Project ID:</span>
                <code className="ml-2 bg-gray-200 px-2 py-1 rounded text-sm">{project.id}</code>
              </div>
              
              <div>
                <span className="font-medium">Correct URL:</span>
                <code className="ml-2 bg-blue-100 px-2 py-1 rounded text-sm">
                  /projects/{project.id}
                </code>
              </div>
              
              <div className="mt-3">
                <Link 
                  href={`/projects/${project.id}`}
                  className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                >
                  View Project Details
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="font-semibold text-yellow-800 mb-2">Important Note:</h3>
        <p className="text-yellow-700">
          Project IDs are now CUID strings (like <code>cm123abc...</code>), not simple numbers like <code>1</code>, <code>2</code>, <code>3</code>.
          Always use the project cards on the main projects page to navigate, or copy the correct URLs from above.
        </p>
      </div>

      <div className="mt-6">
        <Link 
          href="/projects"
          className="inline-block bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
        >
          ← Back to Projects
        </Link>
      </div>
    </div>
  );
}
