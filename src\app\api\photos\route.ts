import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/photos?projectId=xxx
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    console.log('API: Fetching photos', projectId ? `for project ${projectId}` : '');

    const photos = await prisma.photo.findMany({
      where: projectId ? { project_id: projectId } : {},
      orderBy: {
        created_at: 'desc',
      },
    });

    const photosData = photos.map((photo: any) => ({
      id: photo.id,
      title: photo.title,
      date: photo.created_at.toISOString().split('T')[0],
      thumbnail: photo.file_path ? `/uploads/photos/${photo.file_path}` : '',
      file_path: photo.file_path,
      project_id: photo.project_id,
      folder: photo.folder,
    }));

    return NextResponse.json({ data: photosData, error: null });
  } catch (error) {
    console.error('API Error in GET /api/photos:', error);
    return NextResponse.json({ data: null, error: error.message }, { status: 500 });
  }
}

// POST /api/photos
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const projectId = formData.get('projectId') as string;
    const folder = formData.get('folder') as string;
    
    if (!file) {
      return NextResponse.json({ data: null, error: 'No file provided' }, { status: 400 });
    }

    console.log('API: Creating photo record:', file.name);

    // Extract title from filename (without extension)
    const title = file.name.split('.')[0];
    
    // Generate a unique file path
    const fileId = crypto.randomUUID();
    const fileExt = file.name.split('.').pop();
    const filePath = `${fileId}.${fileExt}`;

    // Create photo record in database
    const photo = await prisma.photo.create({
      data: {
        title: title,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.type,
        project_id: projectId || null,
        folder: folder || null,
      },
    });

    const photoData = {
      id: photo.id,
      title: photo.title,
      date: photo.created_at.toISOString().split('T')[0],
      thumbnail: `/uploads/photos/${photo.file_path}`,
      file_path: photo.file_path,
      project_id: photo.project_id,
      folder: photo.folder,
    };

    return NextResponse.json({ data: photoData, error: null });
  } catch (error) {
    console.error('API Error in POST /api/photos:', error);
    return NextResponse.json({ data: null, error: error.message }, { status: 500 });
  }
}
