import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/documents?projectId=xxx
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json({ data: null, error: 'Project ID is required' }, { status: 400 });
    }

    console.log('API: Fetching documents for project:', projectId);

    const documents = await prisma.document.findMany({
      where: {
        project_id: projectId,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    const documentsData = documents.map((doc: any) => ({
      id: doc.id,
      name: doc.name,
      file_path: doc.file_path,
      file_size: doc.file_size,
      mime_type: doc.mime_type,
      folder: doc.folder,
      project_id: doc.project_id,
      created_at: doc.created_at.toISOString(),
      updated_at: doc.updated_at.toISOString(),
    }));

    return NextResponse.json({ data: documentsData, error: null });
  } catch (error) {
    console.error('API Error in GET /api/documents:', error);
    return NextResponse.json({ data: null, error: error.message }, { status: 500 });
  }
}

// POST /api/documents
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const projectId = formData.get('projectId') as string;
    const folder = formData.get('folder') as string;
    
    if (!file) {
      return NextResponse.json({ data: null, error: 'No file provided' }, { status: 400 });
    }

    if (!projectId) {
      return NextResponse.json({ data: null, error: 'Project ID is required' }, { status: 400 });
    }

    console.log('API: Creating document record:', file.name);

    // Generate a unique file path
    const fileId = crypto.randomUUID();
    const fileExt = file.name.split('.').pop();
    const filePath = `${fileId}.${fileExt}`;

    // Create document record in database
    const document = await prisma.document.create({
      data: {
        name: file.name,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.type,
        project_id: projectId,
        folder: folder || null,
      },
    });

    const documentData = {
      id: document.id,
      name: document.name,
      file_path: document.file_path,
      file_size: document.file_size,
      mime_type: document.mime_type,
      folder: document.folder,
      project_id: document.project_id,
      created_at: document.created_at.toISOString(),
      updated_at: document.updated_at.toISOString(),
    };

    return NextResponse.json({ data: documentData, error: null });
  } catch (error) {
    console.error('API Error in POST /api/documents:', error);
    return NextResponse.json({ data: null, error: error.message }, { status: 500 });
  }
}
