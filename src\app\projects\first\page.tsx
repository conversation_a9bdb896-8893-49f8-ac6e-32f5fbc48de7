'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getProjects } from '@/services/project-service';

export default function FirstProjectPage() {
  const router = useRouter();

  useEffect(() => {
    async function redirectToFirstProject() {
      try {
        const { data: projects, error } = await getProjects();
        
        if (error || !projects || projects.length === 0) {
          // If no projects, redirect to projects page
          router.push('/projects');
          return;
        }

        // Redirect to the first project
        const firstProject = projects[0];
        router.push(`/projects/${firstProject.id}`);
      } catch (error) {
        console.error('Error fetching projects:', error);
        router.push('/projects');
      }
    }

    redirectToFirstProject();
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-xl font-semibold mb-4">Redirecting to first project...</h1>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      </div>
    </div>
  );
}
